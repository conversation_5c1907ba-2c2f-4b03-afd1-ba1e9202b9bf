#!/bin/bash

# 简化版本：用于 cron 的 arb 重启脚本
# 添加到 crontab: 0 * * * * /path/to/restart_arb_cron.sh

# 配置
ARB_BINARY="arb"
LOG_FILE="/home/<USER>/codes/libwebsocket-rs/arb_restart_cron.log"
WORK_DIR="/home/<USER>/codes/libwebsocket-rs"

# 切换到工作目录
cd "$WORK_DIR" || exit 1

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

log_message "开始重启 arb 进程"

# 停止现有进程
ARB_PIDS=$(pgrep -f "$ARB_BINARY")
if [ -n "$ARB_PIDS" ]; then
    log_message "停止现有 arb 进程: $ARB_PIDS"
    echo "$ARB_PIDS" | xargs kill -TERM
    sleep 5
    
    # 强制停止仍在运行的进程
    ARB_PIDS=$(pgrep -f "$ARB_BINARY")
    if [ -n "$ARB_PIDS" ]; then
        echo "$ARB_PIDS" | xargs kill -KILL
        log_message "强制停止进程: $ARB_PIDS"
    fi
fi

# 启动新进程
if command -v "$ARB_BINARY" &> /dev/null; then
    nohup "$ARB_BINARY" > arb_output_cron.log 2>&1 &
elif [ -f "./$ARB_BINARY" ]; then
    nohup "./$ARB_BINARY" > arb_output_cron.log 2>&1 &
else
    log_message "错误：找不到 $ARB_BINARY 二进制文件"
    exit 1
fi

NEW_PID=$!
log_message "启动新的 arb 进程，PID: $NEW_PID"

# 验证启动
sleep 2
if kill -0 "$NEW_PID" 2>/dev/null; then
    log_message "arb 进程重启成功"
else
    log_message "错误：arb 进程启动失败"
    exit 1
fi
