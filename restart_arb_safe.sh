#!/bin/bash

# 安全版本的 arb 重启脚本
# 专门处理可能没有 arb 程序的情况
# 使用方法：./restart_arb_safe.sh

# 配置变量
ARB_BINARY="arb"  # arb 二进制文件名
LOG_FILE="arb_restart.log"  # 日志文件
PID_FILE="arb.pid"  # PID 文件

# 获取脚本信息
SCRIPT_NAME=$(basename "$0")
SCRIPT_PID=$$

# 日志函数
log_message() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message"
    # 安全地写入日志文件
    if touch "$LOG_FILE" 2>/dev/null; then
        echo "$message" >> "$LOG_FILE" 2>/dev/null
    fi
}

# 安全的进程查找函数
find_arb_processes() {
    local found_pids=""
    
    # 方法1: 精确匹配进程名
    local exact_pids=$(pgrep -x "$ARB_BINARY" 2>/dev/null)
    
    # 方法2: 如果精确匹配失败，尝试匹配完整路径
    if [ -z "$exact_pids" ]; then
        # 只匹配以 /arb 结尾的进程（避免匹配到包含arb的其他进程）
        local path_pids=$(ps aux | grep -E "[[:space:]].*/$ARB_BINARY(\s|$)" | grep -v grep | awk '{print $2}')
        found_pids="$path_pids"
    else
        found_pids="$exact_pids"
    fi
    
    # 排除当前脚本及其子进程
    if [ -n "$found_pids" ]; then
        local filtered_pids=""
        for pid in $found_pids; do
            # 检查是否是脚本相关进程
            if [ "$pid" != "$SCRIPT_PID" ] && ! ps -p "$pid" -o cmd --no-headers 2>/dev/null | grep -q "$SCRIPT_NAME"; then
                filtered_pids="$filtered_pids $pid"
            fi
        done
        echo "$filtered_pids" | xargs
    fi
}

# 停止单个进程
stop_single_process() {
    local pid=$1
    local timeout=10
    
    if ! kill -0 "$pid" 2>/dev/null; then
        return 0  # 进程已经不存在
    fi
    
    log_message "停止进程 PID: $pid"
    
    # 优雅停止
    kill -TERM "$pid" 2>/dev/null
    
    # 等待进程退出
    local count=0
    while [ $count -lt $timeout ] && kill -0 "$pid" 2>/dev/null; do
        sleep 0.5
        count=$((count + 1))
    done
    
    # 如果还在运行，强制停止
    if kill -0 "$pid" 2>/dev/null; then
        log_message "进程 $pid 未响应 SIGTERM，使用 SIGKILL"
        kill -KILL "$pid" 2>/dev/null
        sleep 1
    fi
    
    # 最终检查
    if kill -0 "$pid" 2>/dev/null; then
        log_message "警告：进程 $pid 仍然存在"
        return 1
    else
        log_message "进程 $pid 已停止"
        return 0
    fi
}

# 停止所有 arb 进程
stop_arb() {
    log_message "正在停止 arb 进程..."
    
    # 首先尝试从PID文件停止
    if [ -f "$PID_FILE" ]; then
        local stored_pid=$(cat "$PID_FILE" 2>/dev/null)
        if [ -n "$stored_pid" ] && kill -0 "$stored_pid" 2>/dev/null; then
            log_message "从PID文件找到进程: $stored_pid"
            stop_single_process "$stored_pid"
        fi
    fi
    
    # 查找其他可能的 arb 进程
    local arb_pids=$(find_arb_processes)
    
    if [ -n "$arb_pids" ]; then
        log_message "找到 arb 进程: $arb_pids"
        for pid in $arb_pids; do
            stop_single_process "$pid"
        done
    else
        log_message "未找到运行中的 arb 进程"
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
    
    # 最终验证
    local remaining=$(find_arb_processes)
    if [ -n "$remaining" ]; then
        log_message "警告：仍有进程未被停止: $remaining"
        return 1
    else
        log_message "所有 arb 进程已停止"
        return 0
    fi
}

# 启动 arb 进程
start_arb() {
    log_message "正在启动 arb 进程..."
    
    # 检查二进制文件是否存在
    local arb_path=""
    if command -v "$ARB_BINARY" >/dev/null 2>&1; then
        arb_path="$ARB_BINARY"
    elif [ -f "./$ARB_BINARY" ]; then
        arb_path="./$ARB_BINARY"
    else
        log_message "错误：找不到 $ARB_BINARY 程序"
        log_message "请确保 $ARB_BINARY 在 PATH 中或当前目录中"
        return 1
    fi
    
    # 确保没有残留进程
    stop_arb
    sleep 1
    
    # 启动新进程
    log_message "使用路径: $arb_path"
    nohup "$arb_path" > arb_output.log 2>&1 &
    local new_pid=$!
    
    # 保存PID
    echo "$new_pid" > "$PID_FILE"
    
    # 验证启动
    sleep 2
    if kill -0 "$new_pid" 2>/dev/null; then
        log_message "arb 进程启动成功，PID: $new_pid"
        return 0
    else
        log_message "错误：arb 进程启动失败"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 重启函数
restart_arb() {
    log_message "开始重启 arb 进程"
    
    if stop_arb && start_arb; then
        log_message "arb 进程重启成功"
    else
        log_message "arb 进程重启失败"
    fi
    
    echo "----------------------------------------" >> "$LOG_FILE" 2>/dev/null
}

# 清理函数
cleanup() {
    log_message "收到退出信号，正在清理..."
    stop_arb
    log_message "脚本退出"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    log_message "arb 安全重启脚本启动"
    log_message "每小时重启一次 arb 进程"
    
    # 首次启动
    start_arb
    
    # 每小时重启循环
    while true; do
        sleep 3600  # 1小时
        restart_arb
    done
}

# 如果直接运行脚本
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
