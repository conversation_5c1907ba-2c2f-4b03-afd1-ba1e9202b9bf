use libc::{
    CPU_SET, CPU_ZERO, SCHED_FIFO, cpu_set_t, pid_t, sched_param, sched_setaffinity,
    sched_setscheduler,
};
use libwebsocket_rs::{
    engine::{arbitrage_runner::run, latency_measurement::measure},
    info, warn,
};
use std::{env, mem};

fn print_usage() {
    info!("Usage: arb [OPTIONS]\n");
    info!("Options:\n");
    info!("  --measure, -m    Run latency measurement before starting arbitrage\n");
    info!("  --no-measure     Skip latency measurement and use default configuration\n");
    info!("  --help, -h       Show this help message\n");
    info!("\n");
    info!("If no options are provided, latency measurement will be run by default.\n");
}

unsafe fn bind_to_core(core_id: usize) {
    unsafe {
        let mut set: cpu_set_t = mem::zeroed();
        CPU_ZERO(&mut set);
        CPU_SET(core_id, &mut set);

        let pid: pid_t = 0; // 0 表示当前线程

        let result = sched_setaffinity(pid, mem::size_of::<cpu_set_t>(), &set);

        if result != 0 {
            warn!(
                "Failed to set CPU affinity: {}",
                std::io::Error::last_os_error()
            );
        } else {
            info!("Successfully bound to core {}", core_id);
        }
    }
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();

    // 解析命令行参数
    let mut run_measure = false; // 默认运行测量

    for arg in args.iter().skip(1) {
        match arg.as_str() {
            "--measure" | "-m" => {
                run_measure = true;
            }
            "--no-measure" => {
                run_measure = false;
            }
            "--help" | "-h" => {
                print_usage();
                return Ok(());
            }
            _ => {
                libwebsocket_rs::error!("Unknown argument: {}", arg);
                print_usage();
                return Err("Invalid arguments".into());
            }
        }
    }

    unsafe {
        bind_to_core(0);
        let param = sched_param { sched_priority: 99 };
        let pid = std::process::id() as i32;
        let ret = sched_setscheduler(pid, SCHED_FIFO, &param);
        if ret != 0 {
            warn!(
                "Warning: Failed to set SCHED_FIFO: {}",
                std::io::Error::last_os_error()
            );
            warn!("running as normal priority...");
        }
    }

    let (best_market_data_ip, best_trade_ip, best_depth_ip, best_order_ip) = if run_measure {
        info!("running latency measurement...");
        let result = measure();
        if result.0.is_none() || result.1.is_none() || result.2.is_none() {
            info!("cannot find best ip, use default config");
        }
        // avoid hit the order placing request limit, sleep 10s
        std::thread::sleep(std::time::Duration::from_secs(10));
        result
    } else {
        info!("skip latency measurement, use default config");
        (None, None, None, None)
    };

    run(
        best_market_data_ip,
        best_trade_ip,
        best_depth_ip,
        best_order_ip,
    )?;

    Ok(())
}
