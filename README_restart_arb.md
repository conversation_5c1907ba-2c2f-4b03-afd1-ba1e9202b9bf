# ARB 自动重启脚本

这个项目包含了三种不同的方式来每小时重启 `arb` 二进制程序。

## 文件说明

1. **restart_arb.sh** - 完整的自动重启脚本，包含循环和日志记录
2. **restart_arb_cron.sh** - 简化版本，配合 cron 使用
3. **arb-restart.service** - systemd 服务文件
4. **README_restart_arb.md** - 本说明文件

## 使用方法

### 方法 1：直接运行脚本（推荐）

```bash
# 给脚本添加执行权限
chmod +x restart_arb.sh

# 运行脚本（会持续运行，每小时重启一次 arb）
./restart_arb.sh

# 后台运行
nohup ./restart_arb.sh > restart_script.log 2>&1 &
```

### 方法 2：使用 systemd 服务

```bash
# 给脚本添加执行权限
chmod +x restart_arb.sh

# 复制服务文件到 systemd 目录
sudo cp arb-restart.service /etc/systemd/system/

# 重新加载 systemd
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start arb-restart

# 设置开机自启
sudo systemctl enable arb-restart

# 查看服务状态
sudo systemctl status arb-restart

# 查看日志
sudo journalctl -u arb-restart -f
```

### 方法 3：使用 cron

```bash
# 给脚本添加执行权限
chmod +x restart_arb_cron.sh

# 编辑 crontab
crontab -e

# 添加以下行（每小时的第0分钟执行）
0 * * * * /home/<USER>/codes/libwebsocket-rs/restart_arb_cron.sh

# 查看 crontab
crontab -l
```

## 脚本功能

### restart_arb.sh 功能：
- 自动查找并停止现有的 arb 进程
- 优雅停止（SIGTERM）+ 强制停止（SIGKILL）
- 启动新的 arb 进程
- 详细的日志记录
- 信号处理（Ctrl+C 时清理）
- 每小时自动重启

### restart_arb_cron.sh 功能：
- 简化版本，适合 cron 调用
- 基本的进程停止和启动
- 简单的日志记录

## 日志文件

- `arb_restart.log` - 主脚本的日志
- `arb_output.log` - arb 程序的输出日志
- `arb_restart_cron.log` - cron 脚本的日志
- `arb_output_cron.log` - cron 版本的 arb 输出日志

## 停止脚本

### 停止直接运行的脚本：
```bash
# 找到脚本进程
ps aux | grep restart_arb.sh

# 发送 SIGTERM 信号
kill -TERM <PID>
```

### 停止 systemd 服务：
```bash
sudo systemctl stop arb-restart
sudo systemctl disable arb-restart  # 取消开机自启
```

### 停止 cron 任务：
```bash
crontab -e  # 删除相应的行
```

## 注意事项

1. 确保 `arb` 二进制文件在 PATH 中或在当前目录
2. 检查文件权限，确保脚本有执行权限
3. 根据需要修改脚本中的路径和配置
4. 建议先测试脚本是否能正确启动和停止 arb 进程

## 自定义配置

可以修改脚本开头的配置变量：
- `ARB_BINARY` - arb 二进制文件名或路径
- `LOG_FILE` - 日志文件路径
- `PID_FILE` - PID 文件路径

## 故障排除

1. 如果 arb 进程无法启动，检查：
   - arb 二进制文件是否存在
   - 是否有执行权限
   - 依赖库是否完整

2. 如果脚本无法停止 arb 进程，检查：
   - 进程名称是否正确
   - 是否有足够的权限

3. 查看日志文件了解详细错误信息
