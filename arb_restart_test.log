[2025-07-20 02:49:00] 开始测试 restart_arb.sh 脚本逻辑
[2025-07-20 02:49:00] 测试日志文件: arb_restart_test.log
[2025-07-20 02:49:00] === 测试进程匹配逻辑 ===
[2025-07-20 02:49:00] 所有包含'arb'的进程：
[2025-07-20 02:49:00] 精确匹配 'arb'：
[2025-07-20 02:49:00] 未找到精确匹配
[2025-07-20 02:49:00] 路径匹配 '^[^ ]*/arb'：
[2025-07-20 02:49:00] 未找到路径匹配
[2025-07-20 02:49:00] 当前脚本PID: 2396145
[2025-07-20 02:49:00] 脚本名称相关进程:
[2025-07-20 02:49:00] === 测试模拟启动/停止 ===
[2025-07-20 02:49:00] === 模拟启动 arb 进程 ===
[2025-07-20 02:49:00] 模拟进程启动成功，PID: 2396171
[2025-07-20 02:49:02] === 模拟停止 arb 进程 ===
[2025-07-20 02:49:02] 停止模拟进程 PID: 2396171
[2025-07-20 02:49:02] 所有测试完成，请检查日志文件: arb_restart_test.log
