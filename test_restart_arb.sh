#!/bin/bash

# 测试版本：用于验证脚本逻辑，不会实际启动arb程序
# 使用方法：./test_restart_arb.sh

# 配置变量
ARB_BINARY="arb"  # arb 二进制文件名
LOG_FILE="arb_restart_test.log"  # 日志文件
PID_FILE="arb_test.pid"  # PID 文件

# 获取脚本的绝对路径，用于排除自身
SCRIPT_NAME=$(basename "$0")
SCRIPT_PID=$$

# 测试模式标志
TEST_MODE=true

# 日志函数
log_message() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message"
    # 安全地写入日志文件，处理权限问题
    if [ -w "$LOG_FILE" ] || [ ! -e "$LOG_FILE" ]; then
        echo "$message" >> "$LOG_FILE" 2>/dev/null
    elif [ -w "$(dirname "$LOG_FILE")" ]; then
        echo "$message" >> "$LOG_FILE" 2>/dev/null
    fi
}

# 测试：检查进程匹配逻辑
test_process_matching() {
    log_message "=== 测试进程匹配逻辑 ==="
    
    # 显示当前所有包含"arb"的进程
    log_message "所有包含'arb'的进程："
    pgrep -f "arb" | while read pid; do
        if [ -n "$pid" ]; then
            ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null || echo "PID $pid 不存在"
        fi
    done
    
    # 测试精确匹配
    log_message "精确匹配 '$ARB_BINARY'："
    EXACT_MATCH=$(pgrep -x "$ARB_BINARY" 2>/dev/null)
    if [ -n "$EXACT_MATCH" ]; then
        log_message "找到精确匹配: $EXACT_MATCH"
    else
        log_message "未找到精确匹配"
    fi
    
    # 测试路径匹配
    log_message "路径匹配 '^[^ ]*/$ARB_BINARY'："
    PATH_MATCH=$(pgrep -f "^[^ ]*/$ARB_BINARY" 2>/dev/null)
    if [ -n "$PATH_MATCH" ]; then
        log_message "找到路径匹配: $PATH_MATCH"
    else
        log_message "未找到路径匹配"
    fi
    
    # 测试排除脚本自身
    log_message "当前脚本PID: $SCRIPT_PID"
    log_message "脚本名称相关进程:"
    pgrep -f "$SCRIPT_NAME" | while read pid; do
        if [ -n "$pid" ]; then
            ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null
        fi
    done
}

# 模拟启动arb进程（测试模式）
simulate_start_arb() {
    log_message "=== 模拟启动 arb 进程 ==="
    
    if [ "$TEST_MODE" = true ]; then
        # 创建一个模拟的后台进程
        sleep 3600 &
        local mock_pid=$!
        echo "$mock_pid" > "$PID_FILE"
        log_message "模拟进程启动成功，PID: $mock_pid"
        return 0
    fi
}

# 模拟停止arb进程（测试模式）
simulate_stop_arb() {
    log_message "=== 模拟停止 arb 进程 ==="
    
    if [ -f "$PID_FILE" ]; then
        local stored_pid=$(cat "$PID_FILE" 2>/dev/null)
        if [ -n "$stored_pid" ] && kill -0 "$stored_pid" 2>/dev/null; then
            log_message "停止模拟进程 PID: $stored_pid"
            kill "$stored_pid" 2>/dev/null
        fi
        rm -f "$PID_FILE"
    fi
}

# 清理函数
cleanup_test() {
    log_message "清理测试环境..."
    simulate_stop_arb
    rm -f "$PID_FILE"
    log_message "测试完成"
    exit 0
}

# 设置信号处理
trap cleanup_test SIGINT SIGTERM

# 主测试函数
main_test() {
    log_message "开始测试 restart_arb.sh 脚本逻辑"
    log_message "测试日志文件: $LOG_FILE"
    
    # 测试1: 进程匹配逻辑
    test_process_matching
    
    echo ""
    log_message "=== 测试模拟启动/停止 ==="
    
    # 测试2: 模拟启动
    simulate_start_arb
    sleep 2
    
    # 测试3: 模拟停止
    simulate_stop_arb
    
    log_message "所有测试完成，请检查日志文件: $LOG_FILE"
}

# 运行测试
main_test
