#!/usr/bin/env python3
import csv
from collections import defaultdict

def analyze_latency_win_loss(csv_file):
    """
    分析CSV文件中延迟大于5ms时的win和loss比例
    """
    # 读取CSV文件
    data = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames
        for row in reader:
            data.append(row)

    print(f"总记录数: {len(data)}")
    print(f"CSV文件列名: {headers}")
    print()

    # 检查延迟列的数据类型和范围
    latency_col = 'latency'
    win_loss_col = 'win_loss'

    # 提取延迟数据并转换为浮点数
    latencies = []
    for row in data:
        try:
            latency = float(row[latency_col])
            latencies.append(latency)
        except (ValueError, KeyError):
            continue

    if not latencies:
        print("没有有效的延迟数据")
        return

    latencies.sort()
    min_latency = min(latencies)
    max_latency = max(latencies)
    avg_latency = sum(latencies) / len(latencies)
    median_latency = latencies[len(latencies)//2]

    print(f"延迟统计信息:")
    print(f"  最小延迟: {min_latency:.2f}")
    print(f"  最大延迟: {max_latency:.2f}")
    print(f"  平均延迟: {avg_latency:.2f}")
    print(f"  中位数延迟: {median_latency:.2f}")
    print()

    # 延迟单位判断 - 从数据看起来是微秒
    # 5ms = 5000微秒
    threshold_us = 5000  # 5ms in microseconds

    print(f"使用阈值: {threshold_us} 微秒 (5ms)")
    print()

    # 筛选延迟大于5ms的记录
    high_latency_data = []
    for row in data:
        try:
            latency = float(row[latency_col])
            if latency > threshold_us:
                high_latency_data.append(row)
        except (ValueError, KeyError):
            continue

    print(f"延迟大于5ms的记录数: {len(high_latency_data)}")
    print(f"占总记录的比例: {len(high_latency_data)/len(data)*100:.2f}%")
    print()

    if len(high_latency_data) == 0:
        print("没有延迟大于5ms的记录")
        return

    # 统计win和loss的数量
    win_loss_counts = defaultdict(int)
    for row in high_latency_data:
        win_loss = row.get(win_loss_col, '')
        if win_loss:
            win_loss_counts[win_loss] += 1

    print("延迟大于5ms时的Win/Loss统计:")
    for key, count in win_loss_counts.items():
        print(f"  {key}: {count}")
    print()

    # 计算比例
    total_high_latency = len(high_latency_data)
    win_count = win_loss_counts.get('Win', 0)
    loss_count = win_loss_counts.get('Loss', 0)

    win_ratio = win_count / total_high_latency * 100 if total_high_latency > 0 else 0
    loss_ratio = loss_count / total_high_latency * 100 if total_high_latency > 0 else 0

    print("延迟大于5ms时的Win/Loss比例:")
    print(f"  Win:  {win_count:4d} 次 ({win_ratio:5.2f}%)")
    print(f"  Loss: {loss_count:4d} 次 ({loss_ratio:5.2f}%)")
    print(f"  总计: {total_high_latency:4d} 次")
    print()

    # 对比所有数据的win/loss比例
    all_win_loss_counts = defaultdict(int)
    for row in data:
        win_loss = row.get(win_loss_col, '')
        if win_loss:
            all_win_loss_counts[win_loss] += 1

    all_win_count = all_win_loss_counts.get('Win', 0)
    all_loss_count = all_win_loss_counts.get('Loss', 0)
    all_total = len(data)

    all_win_ratio = all_win_count / all_total * 100 if all_total > 0 else 0
    all_loss_ratio = all_loss_count / all_total * 100 if all_total > 0 else 0

    print("所有数据的Win/Loss比例 (对比):")
    print(f"  Win:  {all_win_count:4d} 次 ({all_win_ratio:5.2f}%)")
    print(f"  Loss: {all_loss_count:4d} 次 ({all_loss_ratio:5.2f}%)")
    print(f"  总计: {all_total:4d} 次")
    print()

    # 分析延迟分布
    print("延迟分布分析:")
    latency_ranges = [
        (0, 1000, "0-1ms"),
        (1000, 2000, "1-2ms"),
        (2000, 3000, "2-3ms"),
        (3000, 4000, "3-4ms"),
        (4000, 5000, "4-5ms"),
        (5000, 10000, "5-10ms"),
        (10000, 20000, "10-20ms"),
        (20000, float('inf'), ">20ms")
    ]

    for min_lat, max_lat, label in latency_ranges:
        range_data = []
        for row in data:
            try:
                latency = float(row[latency_col])
                if max_lat == float('inf'):
                    if latency >= min_lat:
                        range_data.append(row)
                else:
                    if min_lat <= latency < max_lat:
                        range_data.append(row)
            except (ValueError, KeyError):
                continue

        if len(range_data) > 0:
            range_win_count = sum(1 for row in range_data if row.get(win_loss_col) == 'Win')
            range_loss_count = sum(1 for row in range_data if row.get(win_loss_col) == 'Loss')
            range_total = len(range_data)
            range_win_ratio = range_win_count / range_total * 100 if range_total > 0 else 0

            print(f"  {label:8s}: {range_total:4d} 次, Win: {range_win_count:3d} ({range_win_ratio:5.1f}%), Loss: {range_loss_count:3d}")

if __name__ == "__main__":
    csv_file = "fail-logs/20250722_091716_analysis.csv"
    analyze_latency_win_loss(csv_file)
