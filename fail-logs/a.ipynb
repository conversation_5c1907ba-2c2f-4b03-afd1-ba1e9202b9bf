{"cells": [{"cell_type": "code", "execution_count": 8, "id": "b813a990", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    trigger_symbol            trigger_log_time win_loss  real_rate  \\\n", "0          DOTUSDT  2025-07-17 10:36:08.159191     Loss   0.999455   \n", "1          DOTUSDT  2025-07-17 10:36:08.159191     Loss   0.999455   \n", "2          DOTUSDT  2025-07-17 10:36:08.159191     Loss   0.999455   \n", "3          DOTUSDT  2025-07-17 10:39:29.952218     Loss   0.999657   \n", "4          DOTUSDT  2025-07-17 10:39:29.952218     Loss   0.999657   \n", "..             ...                         ...      ...        ...   \n", "625        ADAUSDT  2025-07-17 23:30:36.174366     Loss   1.000042   \n", "626        ADAUSDT  2025-07-17 23:30:36.174366     Loss   1.000042   \n", "627        ADAUSDT  2025-07-17 23:36:41.925490     Loss   0.999416   \n", "628        ADAUSDT  2025-07-17 23:36:41.925490     Loss   0.999416   \n", "629        ADAUSDT  2025-07-17 23:36:41.925490     Loss   0.999416   \n", "\n", "     expect_rate pair_symbol               pair_log_time direction  \\\n", "0       1.000873    USDCUSDT  2025-07-17 10:36:08.159128       BUY   \n", "1       1.000873     DOTUSDC  2025-07-17 10:36:08.159164       BUY   \n", "2       1.000873     DOTUSDT  2025-07-17 10:36:08.159171      SELL   \n", "3       1.000713     DOTUSDT  2025-07-17 10:39:29.952153      SELL   \n", "4       1.000713     ETHUSDT  2025-07-17 10:39:29.952189       BUY   \n", "..           ...         ...                         ...       ...   \n", "625     1.000765    USDCUSDT  2025-07-17 23:30:36.174352      SELL   \n", "626     1.000765     ADAUSDT  2025-07-17 23:30:36.174360       BUY   \n", "627     1.001825     ADAUSDT  2025-07-17 23:36:41.925427       BUY   \n", "628     1.001825      ADABTC  2025-07-17 23:36:41.925474      SELL   \n", "629     1.001825     BTCUSDT  2025-07-17 23:36:41.925481      SELL   \n", "\n", "             price  quantity              u_ts              e_ts  from  \\\n", "0         0.999600  15.00000  1752748568077133  1752748568076289     1   \n", "1         4.231000   3.54000  1752748567946788  1752748567946540     3   \n", "2         4.233000   3.54000  1752748568159080  1752748568158593     1   \n", "3         4.233000   3.54000  1752748769952116  1752748769951595     1   \n", "4      3467.200000   0.00430  1752748769921973  1752748769921646     1   \n", "..             ...       ...               ...               ...   ...   \n", "625       0.999200  14.00000  1752795035937617  1752795035937380     3   \n", "626       0.830000  16.80000  1752795036174275  1752795036173446     1   \n", "627       0.827600  18.10000  1752795401925395  1752795401907401     4   \n", "628       0.000007  18.10000  1752795400879886  1752795400879581     3   \n", "629  119468.410000   0.00012  1752795401918473  1752795401917165     4   \n", "\n", "              u_ts_formatted           e_ts_formatted  log_u_diff_ms  \\\n", "0    2025-07-17 10:36:08.077  2025-07-17 10:36:08.076         81.995   \n", "1    2025-07-17 10:36:07.946  2025-07-17 10:36:07.946        212.376   \n", "2    2025-07-17 10:36:08.159  2025-07-17 10:36:08.158          0.091   \n", "3    2025-07-17 10:39:29.952  2025-07-17 10:39:29.951          0.037   \n", "4    2025-07-17 10:39:29.921  2025-07-17 10:39:29.921         30.216   \n", "..                       ...                      ...            ...   \n", "625  2025-07-17 23:30:35.937  2025-07-17 23:30:35.937        236.735   \n", "626  2025-07-17 23:30:36.174  2025-07-17 23:30:36.173          0.085   \n", "627  2025-07-17 23:36:41.925  2025-07-17 23:36:41.907          0.032   \n", "628  2025-07-17 23:36:40.879  2025-07-17 23:36:40.879       1045.588   \n", "629  2025-07-17 23:36:41.918  2025-07-17 23:36:41.917          7.008   \n", "\n", "     u_e_diff_ms  \n", "0          0.844  \n", "1          0.248  \n", "2          0.487  \n", "3          0.521  \n", "4          0.327  \n", "..           ...  \n", "625        0.237  \n", "626        0.829  \n", "627       17.994  \n", "628        0.305  \n", "629        1.308  \n", "\n", "[630 rows x 17 columns]\n"]}], "source": ["import pandas as pd\n", "\n", "data = pd.read_csv(\"result_with_expect_rate.csv\")\n", "print(data)"]}, {"cell_type": "code", "execution_count": 9, "id": "86bb16ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["win latency 0.727\n", "loss latency 0.674\n"]}], "source": ["win_latency = data[data['win_loss'] == \"Win\"]['u_e_diff_ms'].median()\n", "loss_latency = data[data['win_loss'] == \"Loss\"]['u_e_diff_ms'].median()\n", "\n", "print(f\"win latency {win_latency}\")\n", "print(f\"loss latency {loss_latency}\")"]}, {"cell_type": "code", "execution_count": 10, "id": "817bd403", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["win rate avg 24     1.000731\n", "25     1.000731\n", "26     1.000731\n", "36     1.001129\n", "37     1.001129\n", "         ...   \n", "595    1.000819\n", "596    1.000819\n", "609    1.000720\n", "610    1.000720\n", "611    1.000720\n", "Name: real_rate, Length: 141, dtype: float64\n", "loss rate avg 0      0.999455\n", "1      0.999455\n", "2      0.999455\n", "3      0.999657\n", "4      0.999657\n", "         ...   \n", "625    1.000042\n", "626    1.000042\n", "627    0.999416\n", "628    0.999416\n", "629    0.999416\n", "Name: real_rate, Length: 483, dtype: float64\n"]}], "source": ["win_rate_avg = data[data['win_loss'] == \"Win\"]['real_rate']\n", "loss_rate_avg = data[data['win_loss'] == \"Loss\"]['real_rate']\n", "\n", "print(f\"win rate avg {win_rate_avg}\")\n", "print(f\"loss rate avg {loss_rate_avg}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}