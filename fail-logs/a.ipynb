import pandas as pd

pnl = 0
max_pnl = 0
min_pnl = 0
data = pd.read_csv("20250722_091716_analysis.csv")


win_rate_avg = data[data['win_loss'] == "Win"][data['real_rate'] < 1.01]['real_rate'].mean()
loss_rate_avg = data[data['win_loss'] == "Loss"]['real_rate'].mean()

print(f"win rate avg {win_rate_avg}")
print(f"loss rate avg {loss_rate_avg}")

data[data['win_loss'] == "Win"]['real_rate'][data['real_rate'] < 1.01].count() / 3