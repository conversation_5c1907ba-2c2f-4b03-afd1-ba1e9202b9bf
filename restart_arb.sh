#!/bin/bash

# 脚本：每小时重启 arb 程序
# 使用方法：./restart_arb.sh

# 配置变量
ARB_BINARY="arb"  # arb 二进制文件名
LOG_FILE="arb_restart.log"  # 日志文件
PID_FILE="arb.pid"  # PID 文件

# 获取脚本的绝对路径，用于排除自身
SCRIPT_NAME=$(basename "$0")
SCRIPT_PID=$$

# 初始化检查函数
init_check() {
    # 检查是否在新机器上首次运行（没有arb进程的情况）
    if ! command -v "$ARB_BINARY" &> /dev/null && [ ! -f "./$ARB_BINARY" ]; then
        log_message "警告：在当前目录和PATH中都找不到 $ARB_BINARY 二进制文件"
        log_message "请确保 $ARB_BINARY 程序已正确安装或放置在当前目录中"
        return 1
    fi

    # 创建日志文件（如果不存在）
    touch "$LOG_FILE" 2>/dev/null || {
        log_message "警告：无法创建日志文件 $LOG_FILE，将只输出到控制台"
    }

    return 0
}

# 日志函数
log_message() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message"
    # 安全地写入日志文件，处理权限问题
    if [ -w "$LOG_FILE" ] || [ ! -e "$LOG_FILE" ]; then
        echo "$message" >> "$LOG_FILE" 2>/dev/null
    elif [ -w "$(dirname "$LOG_FILE")" ]; then
        echo "$message" >> "$LOG_FILE" 2>/dev/null
    fi
}

# 查找并停止 arb 进程
stop_arb() {
    log_message "正在停止 arb 进程..."

    # 首先尝试从PID文件读取PID
    if [ -f "$PID_FILE" ]; then
        STORED_PID=$(cat "$PID_FILE" 2>/dev/null)
        if [ -n "$STORED_PID" ] && kill -0 "$STORED_PID" 2>/dev/null; then
            log_message "从PID文件找到进程 PID: $STORED_PID"
            stop_process_by_pid "$STORED_PID"
        fi
    fi

    # 查找所有匹配的 arb 进程（更精确的匹配，排除脚本相关进程）
    # 首先尝试精确匹配进程名
    ARB_PIDS=$(pgrep -x "$ARB_BINARY" 2>/dev/null)

    # 如果精确匹配没找到，尝试匹配可执行文件路径
    if [ -z "$ARB_PIDS" ]; then
        ARB_PIDS=$(pgrep -f "/$ARB_BINARY(\s|$)" 2>/dev/null)
    fi

    # 排除当前脚本及其子进程
    if [ -n "$ARB_PIDS" ]; then
        # 获取当前脚本的所有相关进程
        SCRIPT_PIDS=$(pgrep -f "$SCRIPT_NAME" 2>/dev/null | tr '\n' '|' | sed 's/|$//')
        if [ -n "$SCRIPT_PIDS" ]; then
            ARB_PIDS=$(echo "$ARB_PIDS" | grep -vE "^($SCRIPT_PIDS)$")
        fi
    fi

    if [ -n "$ARB_PIDS" ]; then
        log_message "找到 arb 进程 PIDs: $ARB_PIDS"

        # 逐个停止所有匹配的进程
        for pid in $ARB_PIDS; do
            if kill -0 "$pid" 2>/dev/null; then
                log_message "停止进程 PID: $pid"
                stop_process_by_pid "$pid"
            fi
        done

        # 最终验证：确保所有arb进程都被杀死
        sleep 1
        REMAINING_PIDS=$(pgrep -x "$ARB_BINARY" 2>/dev/null)
        if [ -z "$REMAINING_PIDS" ]; then
            REMAINING_PIDS=$(pgrep -f "/$ARB_BINARY(\s|$)" 2>/dev/null)
        fi
        # 排除脚本相关进程
        if [ -n "$REMAINING_PIDS" ]; then
            SCRIPT_PIDS=$(pgrep -f "$SCRIPT_NAME" 2>/dev/null | tr '\n' '|' | sed 's/|$//')
            if [ -n "$SCRIPT_PIDS" ]; then
                REMAINING_PIDS=$(echo "$REMAINING_PIDS" | grep -vE "^($SCRIPT_PIDS)$")
            fi
        fi
        if [ -n "$REMAINING_PIDS" ]; then
            log_message "警告：仍有进程未被杀死，强制清理: $REMAINING_PIDS"
            for pid in $REMAINING_PIDS; do
                kill -KILL "$pid" 2>/dev/null
                # 杀死进程组（处理子进程）
                kill -KILL "-$pid" 2>/dev/null
            done
            sleep 2
        fi

        log_message "arb 进程已停止"
    else
        log_message "未找到运行中的 arb 进程"
    fi

    # 清理PID文件
    rm -f "$PID_FILE"
}

# 停止单个进程的辅助函数
stop_process_by_pid() {
    local pid=$1

    # 优雅停止
    kill -TERM "$pid" 2>/dev/null

    # 等待进程响应SIGTERM
    local count=0
    while [ $count -lt 10 ] && kill -0 "$pid" 2>/dev/null; do
        sleep 0.5
        count=$((count + 1))
    done

    # 如果进程仍在运行，使用SIGKILL
    if kill -0 "$pid" 2>/dev/null; then
        log_message "进程 $pid 未响应 SIGTERM，使用 SIGKILL 强制停止"
        kill -KILL "$pid" 2>/dev/null
        # 同时杀死进程组（处理子进程）
        kill -KILL "-$pid" 2>/dev/null
        sleep 1

        # 最终检查
        if kill -0 "$pid" 2>/dev/null; then
            log_message "警告：进程 $pid 仍然存在，可能需要手动处理"
        fi
    fi
}

# 启动 arb 进程
start_arb() {
    log_message "正在启动 arb 进程..."

    # 确保之前的进程已完全停止
    stop_arb

    # 检查 arb 二进制文件是否存在
    if ! command -v "$ARB_BINARY" &> /dev/null && [ ! -f "./$ARB_BINARY" ]; then
        log_message "错误：找不到 $ARB_BINARY 二进制文件"
        return 1
    fi

    # 创建新的进程组来启动 arb（便于管理子进程）
    if command -v "$ARB_BINARY" &> /dev/null; then
        setsid nohup "$ARB_BINARY" > arb_output.log 2>&1 &
    else
        setsid nohup "./$ARB_BINARY" > arb_output.log 2>&1 &
    fi

    ARB_PID=$!
    echo "$ARB_PID" > "$PID_FILE"

    # 验证进程是否成功启动（多次检查确保稳定）
    local count=0
    local max_attempts=5
    while [ $count -lt $max_attempts ]; do
        sleep 1
        if kill -0 "$ARB_PID" 2>/dev/null; then
            log_message "arb 进程启动成功，PID: $ARB_PID"
            return 0
        fi
        count=$((count + 1))
        log_message "等待进程启动... ($count/$max_attempts)"
    done

    log_message "错误：arb 进程启动失败"
    rm -f "$PID_FILE"
    return 1
}

# 强制清理所有相关进程
force_cleanup() {
    log_message "执行强制清理..."

    # 获取要清理的进程列表
    CLEANUP_PIDS=$(pgrep -x "$ARB_BINARY" 2>/dev/null)
    if [ -z "$CLEANUP_PIDS" ]; then
        CLEANUP_PIDS=$(pgrep -f "/$ARB_BINARY(\s|$)" 2>/dev/null)
    fi

    # 排除脚本相关进程
    if [ -n "$CLEANUP_PIDS" ]; then
        SCRIPT_PIDS=$(pgrep -f "$SCRIPT_NAME" 2>/dev/null | tr '\n' '|' | sed 's/|$//')
        if [ -n "$SCRIPT_PIDS" ]; then
            CLEANUP_PIDS=$(echo "$CLEANUP_PIDS" | grep -vE "^($SCRIPT_PIDS)$")
        fi
    fi

    # 逐个强制杀死进程
    if [ -n "$CLEANUP_PIDS" ]; then
        log_message "强制清理进程: $CLEANUP_PIDS"
        for pid in $CLEANUP_PIDS; do
            if [ -n "$pid" ] && [ "$pid" != "$SCRIPT_PID" ]; then
                kill -KILL "$pid" 2>/dev/null
                kill -KILL "-$pid" 2>/dev/null  # 杀死进程组
            fi
        done
        sleep 1
    fi

    # 清理PID文件
    rm -f "$PID_FILE"
}

# 重启 arb 进程
restart_arb() {
    log_message "开始重启 arb 进程"

    # 停止现有进程
    stop_arb

    # 额外等待确保进程完全退出
    sleep 3

    # 如果还有残留进程，强制清理
    REMAINING_CHECK=$(pgrep -x "$ARB_BINARY" 2>/dev/null)
    if [ -z "$REMAINING_CHECK" ]; then
        REMAINING_CHECK=$(pgrep -f "/$ARB_BINARY(\s|$)" 2>/dev/null)
    fi
    # 排除脚本进程
    if [ -n "$REMAINING_CHECK" ]; then
        SCRIPT_PIDS=$(pgrep -f "$SCRIPT_NAME" 2>/dev/null | tr '\n' '|' | sed 's/|$//')
        if [ -n "$SCRIPT_PIDS" ]; then
            REMAINING_CHECK=$(echo "$REMAINING_CHECK" | grep -vE "^($SCRIPT_PIDS)$")
        fi
    fi

    if [ -n "$REMAINING_CHECK" ]; then
        log_message "检测到残留进程，执行强制清理"
        force_cleanup
        sleep 2
    fi

    # 启动新进程
    if start_arb; then
        log_message "arb 进程重启成功"
    else
        log_message "错误：arb 进程重启失败"
        # 可以在这里添加重试逻辑或发送告警
    fi

    echo "----------------------------------------" >> "$LOG_FILE"
}

# 主循环
main() {
    # 初始化检查
    if ! init_check; then
        log_message "初始化检查失败，脚本退出"
        exit 1
    fi

    log_message "arb 自动重启脚本启动"
    log_message "每小时重启一次 arb 进程"

    # 首次启动
    start_arb

    # 每小时重启循环
    while true; do
        # 等待 1 小时（3600 秒）
        sleep 3600
        restart_arb
    done
}

# 信号处理函数
cleanup() {
    log_message "收到退出信号，正在清理..."

    # 停止所有arb进程
    stop_arb

    # 如果还有残留，强制清理
    FINAL_CHECK=$(pgrep -x "$ARB_BINARY" 2>/dev/null)
    if [ -z "$FINAL_CHECK" ]; then
        FINAL_CHECK=$(pgrep -f "/$ARB_BINARY(\s|$)" 2>/dev/null)
    fi
    # 排除脚本进程
    if [ -n "$FINAL_CHECK" ]; then
        SCRIPT_PIDS=$(pgrep -f "$SCRIPT_NAME" 2>/dev/null | tr '\n' '|' | sed 's/|$//')
        if [ -n "$SCRIPT_PIDS" ]; then
            FINAL_CHECK=$(echo "$FINAL_CHECK" | grep -vE "^($SCRIPT_PIDS)$")
        fi
    fi

    if [ -n "$FINAL_CHECK" ]; then
        log_message "执行最终强制清理"
        force_cleanup
    fi

    # 清理文件
    rm -f "$PID_FILE"

    log_message "脚本退出"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 检查是否以 root 权限运行（如果需要的话）
# if [ "$EUID" -ne 0 ]; then
#     echo "请以 root 权限运行此脚本"
#     exit 1
# fi

# 运行主函数
main
