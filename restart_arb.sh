#!/bin/bash

# 脚本：每小时重启 arb 程序
# 使用方法：./restart_arb.sh

# 配置变量
ARB_BINARY="arb"  # arb 二进制文件名
LOG_FILE="arb_restart.log"  # 日志文件
PID_FILE="arb.pid"  # PID 文件

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 查找并停止 arb 进程
stop_arb() {
    log_message "正在停止 arb 进程..."
    
    # 查找 arb 进程
    ARB_PID=$(pgrep -f "$ARB_BINARY")
    
    if [ -n "$ARB_PID" ]; then
        log_message "找到 arb 进程 PID: $ARB_PID"
        
        # 优雅停止
        kill -TERM "$ARB_PID"
        sleep 5
        
        # 检查进程是否还在运行
        if kill -0 "$ARB_PID" 2>/dev/null; then
            log_message "进程未响应 SIGTERM，使用 SIGKILL 强制停止"
            kill -KILL "$ARB_PID"
            sleep 2
        fi
        
        log_message "arb 进程已停止"
    else
        log_message "未找到运行中的 arb 进程"
    fi
}

# 启动 arb 进程
start_arb() {
    log_message "正在启动 arb 进程..."
    
    # 检查 arb 二进制文件是否存在
    if ! command -v "$ARB_BINARY" &> /dev/null && [ ! -f "./$ARB_BINARY" ]; then
        log_message "错误：找不到 $ARB_BINARY 二进制文件"
        return 1
    fi
    
    # 启动 arb（后台运行）
    if command -v "$ARB_BINARY" &> /dev/null; then
        nohup "$ARB_BINARY" > arb_output.log 2>&1 &
    else
        nohup "./$ARB_BINARY" > arb_output.log 2>&1 &
    fi
    
    ARB_PID=$!
    echo "$ARB_PID" > "$PID_FILE"
    
    # 验证进程是否成功启动
    sleep 2
    if kill -0 "$ARB_PID" 2>/dev/null; then
        log_message "arb 进程启动成功，PID: $ARB_PID"
        return 0
    else
        log_message "错误：arb 进程启动失败"
        return 1
    fi
}

# 重启 arb 进程
restart_arb() {
    log_message "开始重启 arb 进程"
    stop_arb
    sleep 2
    start_arb
    log_message "arb 进程重启完成"
    echo "----------------------------------------" >> "$LOG_FILE"
}

# 主循环
main() {
    log_message "arb 自动重启脚本启动"
    log_message "每小时重启一次 arb 进程"
    
    # 首次启动
    start_arb
    
    # 每小时重启循环
    while true; do
        # 等待 1 小时（3600 秒）
        sleep 3600
        restart_arb
    done
}

# 信号处理函数
cleanup() {
    log_message "收到退出信号，正在清理..."
    stop_arb
    rm -f "$PID_FILE"
    log_message "脚本退出"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 检查是否以 root 权限运行（如果需要的话）
# if [ "$EUID" -ne 0 ]; then
#     echo "请以 root 权限运行此脚本"
#     exit 1
# fi

# 运行主函数
main
