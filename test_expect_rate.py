#!/usr/bin/env python3
"""
测试 analyze_fail_logs.py 脚本的 expect_rate 提取功能
"""

import tempfile
import os
import sys

# 添加 fail-logs 目录到路径
sys.path.append('fail-logs')

from analyze_fail_logs import parse_log_file

def test_expect_rate_extraction():
    """测试 expect_rate 提取功能"""
    
    # 创建测试日志内容
    test_log_content = """[2025-07-22 09:24:57.418323] INFO - Trigger symbol: XLMETH
[2025-07-22 09:24:57.419000] INFO - Expect rate: 1.0025
[2025-07-22 09:24:57.420000] INFO - XLMUSDT p: 0.455 t: 1753176297419 l: 4233.022857142857
[2025-07-22 09:24:57.421000] INFO - XLMETH p: 0.00012534 t: 1753176297420 l: 4602.651428571428
[2025-07-22 09:24:57.422000] INFO - ETHUSDT p: 3629.49 t: 1753176297420 l: 4638.132380952381
[2025-07-22 09:24:57.423000] INFO - Real rate: 1.0001752469698557
[2025-07-22 09:24:57.424000] INFO - Loss!

[2025-07-22 10:13:48.705242] INFO - Trigger symbol: ETHBTC
[2025-07-22 10:13:48.706000] INFO - Expect rate: 1.0020
[2025-07-22 10:13:48.707000] INFO - BTCUSDT p: 118404.41 t: 1753179228704 l: 1474.3019047619048
[2025-07-22 10:13:48.708000] INFO - ETHBTC p: 0.03102 t: 1753179228705 l: 1541.9419047619049
[2025-07-22 10:13:48.709000] INFO - ETHUSDT p: 3667.78 t: 1753179228705 l: 1899.5285714285715
[2025-07-22 10:13:48.710000] INFO - Real rate: 1.0013972479810676
[2025-07-22 10:13:48.711000] INFO - Win!
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
        f.write(test_log_content)
        temp_file = f.name
    
    try:
        # 解析日志文件
        results = parse_log_file(temp_file)
        
        print("=== 测试结果 ===")
        print(f"解析到 {len(results)} 个trigger symbol事件")
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. Trigger Symbol: {result['trigger_symbol']}")
            print(f"   Win/Loss: {result.get('win_loss', 'N/A')}")
            print(f"   Real Rate: {result.get('real_rate', 'N/A')}")
            print(f"   Expect Rate: {result.get('expect_rate', 'N/A')}")
            print(f"   交易对数量: {len(result['trading_pairs'])}")
            
            # 验证 expect_rate 是否正确提取
            if result['trigger_symbol'] == 'XLMETH':
                expected_expect_rate = 1.0025
                actual_expect_rate = result.get('expect_rate')
                if actual_expect_rate == expected_expect_rate:
                    print(f"   ✓ Expect Rate 提取正确: {actual_expect_rate}")
                else:
                    print(f"   ✗ Expect Rate 提取错误: 期望 {expected_expect_rate}, 实际 {actual_expect_rate}")
            
            elif result['trigger_symbol'] == 'ETHBTC':
                expected_expect_rate = 1.0020
                actual_expect_rate = result.get('expect_rate')
                if actual_expect_rate == expected_expect_rate:
                    print(f"   ✓ Expect Rate 提取正确: {actual_expect_rate}")
                else:
                    print(f"   ✗ Expect Rate 提取错误: 期望 {expected_expect_rate}, 实际 {actual_expect_rate}")
        
        print("\n=== 测试完成 ===")
        
    finally:
        # 清理临时文件
        os.unlink(temp_file)

if __name__ == "__main__":
    test_expect_rate_extraction()
