# analyze_fail_logs.py 使用说明

## 功能概述

`fail-logs/analyze_fail_logs.py` 脚本用于分析fail-logs日志文件，提取套利交易的详细信息，包括：

- **Trigger Symbol**: 触发套利的交易对
- **Win/Loss**: 套利结果
- **Real Rate**: 实际汇率
- **Expect Rate**: 期望汇率 ⭐ **新增功能**
- **交易对信息**: 相关的所有交易对及其价格、时间戳、延迟等
- **时间分析**: 日志时间与时间戳的差值分析

## 新增功能

### Expect Rate 提取

脚本现在可以从日志中提取 `Expect rate` 信息，这对于分析套利策略的预期收益率非常有用。

**支持的日志格式：**
```
[2025-07-22 09:24:57.418323] INFO - Trigger symbol: XLMETH
[2025-07-22 09:24:57.419000] INFO - Expect rate: 1.0025
[2025-07-22 09:24:57.420000] INFO - XLMUSDT p: 0.455 t: 1753176297419 l: 4233.022857142857
[2025-07-22 09:24:57.423000] INFO - Real rate: 1.0001752469698557
[2025-07-22 09:24:57.424000] INFO - Loss!
```

## 使用方法

### 基本用法

```bash
# 分析日志文件并输出到CSV
python3 fail-logs/analyze_fail_logs.py your_log_file.log

# 指定输出文件名
python3 fail-logs/analyze_fail_logs.py your_log_file.log --output analysis_result.csv

# 同时输出JSON格式
python3 fail-logs/analyze_fail_logs.py your_log_file.log --json analysis_result.json

# 显示详细信息
python3 fail-logs/analyze_fail_logs.py your_log_file.log --verbose
```

### 参数说明

- `log_file`: 必需参数，日志文件路径
- `--output, -o`: 输出CSV文件路径（默认：analysis_result.csv）
- `--json`: 输出JSON文件路径（可选）
- `--verbose, -v`: 显示详细信息

## 输出格式

### CSV输出字段

| 字段名 | 描述 |
|--------|------|
| trigger_symbol | 触发套利的交易对 |
| trigger_log_time | 触发时间 |
| win_loss | 套利结果 (Win/Loss) |
| real_rate | 实际汇率 |
| **expect_rate** | **期望汇率** ⭐ |
| pair_symbol | 相关交易对 |
| pair_log_time | 交易对日志时间 |
| price | 价格 |
| timestamp | 时间戳 |
| latency | 延迟 (ms) |
| timestamp_formatted | 格式化的时间戳 |
| log_timestamp_diff_ms | 日志时间与时间戳差值 (ms) |

### 统计摘要

脚本会自动输出以下统计信息：

1. **总体统计**
   - 总记录数
   - Win/Loss 统计和胜率

2. **Trigger Symbol 统计**
   - 每个交易对的触发次数
   - 各自的胜率

3. **交易对分析**
   - 涉及的所有交易对
   - 交易对总数

## 使用示例

### 示例1：基本分析

```bash
python3 fail-logs/analyze_fail_logs.py fail-logs/20250722_091716.log
```

输出：
```
正在分析日志文件: fail-logs/20250722_091716.log

=== 日志分析摘要 ===
总共找到 150 个trigger symbol事件

Win/Loss 统计:
  Win: 65 次
  Loss: 85 次
  胜率: 43.33%

Trigger Symbol 统计:
  XLMETH: 25 次 (Win: 10, Loss: 15, 胜率: 40.0%)
  ETHBTC: 20 次 (Win: 12, Loss: 8, 胜率: 60.0%)
  ...

结果已保存到CSV文件: analysis_result.csv
```

### 示例2：详细分析

```bash
python3 fail-logs/analyze_fail_logs.py fail-logs/20250722_091716.log --verbose --json detailed_analysis.json
```

会显示每个trigger symbol的详细信息，包括：
- 触发时间
- Win/Loss结果
- Real Rate 和 Expect Rate
- 所有相关交易对的详细信息

## 数据分析建议

### 1. 期望收益率 vs 实际收益率分析

```python
import pandas as pd

# 读取CSV结果
df = pd.read_csv('analysis_result.csv')

# 计算期望收益率与实际收益率的差异
df['rate_diff'] = df['real_rate'] - df['expect_rate']

# 分析差异分布
print("收益率差异统计:")
print(df['rate_diff'].describe())
```

### 2. 胜率与期望收益率关系

```python
# 按期望收益率分组分析胜率
expect_rate_bins = pd.cut(df['expect_rate'], bins=5)
win_rate_by_expect = df.groupby(expect_rate_bins)['win_loss'].apply(
    lambda x: (x == 'Win').sum() / len(x) * 100
)
print("不同期望收益率区间的胜率:")
print(win_rate_by_expect)
```

### 3. 延迟对收益率的影响

```python
# 分析延迟与收益率差异的关系
high_latency = df[df['latency'] > 5000]  # 延迟大于5ms
low_latency = df[df['latency'] <= 5000]

print(f"高延迟胜率: {(high_latency['win_loss'] == 'Win').mean() * 100:.2f}%")
print(f"低延迟胜率: {(low_latency['win_loss'] == 'Win').mean() * 100:.2f}%")
```

## 注意事项

1. **日志格式**: 脚本依赖特定的日志格式，确保日志包含所需的字段
2. **时间戳**: 脚本假设时间戳为微秒级别
3. **编码**: 支持UTF-8编码的日志文件
4. **内存使用**: 对于大型日志文件，建议分批处理

## 故障排除

1. **找不到Expect rate**: 检查日志格式是否包含 "Expect rate: X.XXXX" 格式的行
2. **解析错误**: 确认日志文件编码为UTF-8
3. **输出为空**: 检查日志文件是否包含 "Trigger symbol:" 行

## 更新日志

- **v1.1**: 新增 expect_rate 字段提取功能
- **v1.0**: 基础功能，支持 real_rate、win_loss、交易对信息提取
